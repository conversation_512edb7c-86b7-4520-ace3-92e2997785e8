#!/usr/bin/env python3
"""
Data Flow Validation Script

Validates that the standardized data structure organization is working correctly
and that data flows properly between phases according to the established contracts.
"""

import sys
import json
from pathlib import Path
from typing import Dict, List, Any, Optional

# Simple path manager implementation for validation
class SimplePathManager:
    """Simplified path manager for validation purposes"""

    def __init__(self):
        self.workspace_root = Path.cwd()

    def get_phase_output_path(self, phase_number: int) -> Path:
        """Get output path for specific phase"""
        return self.workspace_root / "data" / f"phase_{phase_number}"


class DataFlowValidator:
    """Validates data flow between phases according to standardized contracts"""
    
    def __init__(self):
        self.path_manager = SimplePathManager()
        self.validation_results = {}
        
    def validate_all_phases(self) -> Dict[str, Any]:
        """Validate data structure organization for all phases"""
        print("🔍 Starting comprehensive data flow validation...")
        print("=" * 60)
        
        # Validate directory structure
        self._validate_directory_structure()
        
        # Validate path manager consistency
        self._validate_path_manager_consistency()
        
        # Validate inter-phase data contracts
        self._validate_inter_phase_contracts()
        
        # Generate summary report
        return self._generate_validation_report()
    
    def _validate_directory_structure(self):
        """Validate that all required directories exist and follow standards"""
        print("\n📁 Validating Directory Structure...")
        
        # Check root data directory
        data_root = Path("data")
        if not data_root.exists():
            self.validation_results["directory_structure"] = {
                "status": "FAILED",
                "error": "Root data directory does not exist"
            }
            return
        
        # Check phase directories
        phase_dirs = {}
        for phase in range(1, 7):
            phase_dir = data_root / f"phase_{phase}"
            phase_dirs[f"phase_{phase}"] = {
                "exists": phase_dir.exists(),
                "path": str(phase_dir),
                "is_directory": phase_dir.is_dir() if phase_dir.exists() else False
            }
            
            if phase_dir.exists():
                print(f"  ✅ Phase {phase} directory: {phase_dir}")
            else:
                print(f"  📝 Phase {phase} directory: {phase_dir} (will be created when needed)")
        
        # Check raw data directory
        raw_dir = data_root / "raw" / "ese"
        raw_data_status = {
            "exists": raw_dir.exists(),
            "path": str(raw_dir),
            "file_count": len(list(raw_dir.rglob("*.tja"))) if raw_dir.exists() else 0
        }
        
        if raw_dir.exists():
            print(f"  ✅ Raw data directory: {raw_dir} ({raw_data_status['file_count']} TJA files)")
        else:
            print(f"  ❌ Raw data directory: {raw_dir} (missing)")
        
        self.validation_results["directory_structure"] = {
            "status": "PASSED",
            "phase_directories": phase_dirs,
            "raw_data": raw_data_status
        }
    
    def _validate_path_manager_consistency(self):
        """Validate that path manager returns consistent paths"""
        print("\n🛠️  Validating Path Manager Consistency...")
        
        path_consistency = {}
        
        # Test phase output paths
        for phase in range(1, 7):
            expected_path = Path("data") / f"phase_{phase}"
            actual_path = self.path_manager.get_phase_output_path(phase)
            
            is_consistent = expected_path.resolve() == actual_path.resolve()
            path_consistency[f"phase_{phase}"] = {
                "expected": str(expected_path),
                "actual": str(actual_path),
                "consistent": is_consistent
            }
            
            if is_consistent:
                print(f"  ✅ Phase {phase} path: {actual_path}")
            else:
                print(f"  ❌ Phase {phase} path mismatch: expected {expected_path}, got {actual_path}")
        
        all_consistent = all(p["consistent"] for p in path_consistency.values())
        
        self.validation_results["path_manager_consistency"] = {
            "status": "PASSED" if all_consistent else "FAILED",
            "paths": path_consistency
        }
    
    def _validate_inter_phase_contracts(self):
        """Validate data contracts between phases"""
        print("\n🔗 Validating Inter-Phase Data Contracts...")
        
        contracts = {
            "phase_1_to_2": self._validate_phase_1_to_2_contract(),
            "phase_2_to_3": self._validate_phase_2_to_3_contract(),
            "phase_3_to_4": self._validate_phase_3_to_4_contract(),
            "phase_4_to_5": self._validate_phase_4_to_5_contract(),
            "phase_5_to_6": self._validate_phase_5_to_6_contract()
        }
        
        self.validation_results["inter_phase_contracts"] = contracts
    
    def _validate_phase_1_to_2_contract(self) -> Dict[str, Any]:
        """Validate Phase 1 → Phase 2 data contract"""
        phase_1_output = self.path_manager.get_phase_output_path(1)
        
        expected_files = [
            "catalog.json",
            "notation_data/",
            "metadata/",
            "validation_report.json"
        ]
        
        contract_status = {
            "expected_files": expected_files,
            "found_files": [],
            "missing_files": [],
            "status": "NOT_TESTED"
        }
        
        if phase_1_output.exists():
            for expected_file in expected_files:
                file_path = phase_1_output / expected_file
                if file_path.exists():
                    contract_status["found_files"].append(expected_file)
                    print(f"    ✅ Found: {expected_file}")
                else:
                    contract_status["missing_files"].append(expected_file)
                    print(f"    📝 Missing: {expected_file} (will be created when Phase 1 runs)")
            
            contract_status["status"] = "READY" if not contract_status["missing_files"] else "PENDING"
        else:
            contract_status["status"] = "PENDING"
            contract_status["missing_files"] = expected_files
            print(f"    📝 Phase 1 output directory not found (will be created when Phase 1 runs)")
        
        return contract_status
    
    def _validate_phase_2_to_3_contract(self) -> Dict[str, Any]:
        """Validate Phase 2 → Phase 3 data contract"""
        return {"status": "PENDING", "note": "Phase 2 not yet executed"}
    
    def _validate_phase_3_to_4_contract(self) -> Dict[str, Any]:
        """Validate Phase 3 → Phase 4 data contract"""
        return {"status": "PENDING", "note": "Phase 3 not yet executed"}
    
    def _validate_phase_4_to_5_contract(self) -> Dict[str, Any]:
        """Validate Phase 4 → Phase 5 data contract"""
        return {"status": "PENDING", "note": "Phase 4 not yet executed"}
    
    def _validate_phase_5_to_6_contract(self) -> Dict[str, Any]:
        """Validate Phase 5 → Phase 6 data contract"""
        return {"status": "PENDING", "note": "Phase 5 not yet executed"}
    
    def _generate_validation_report(self) -> Dict[str, Any]:
        """Generate comprehensive validation report"""
        print("\n📊 Validation Summary")
        print("=" * 60)
        
        # Count results
        total_checks = 0
        passed_checks = 0
        failed_checks = 0
        pending_checks = 0
        
        for category, results in self.validation_results.items():
            if isinstance(results, dict) and "status" in results:
                total_checks += 1
                if results["status"] == "PASSED":
                    passed_checks += 1
                    print(f"✅ {category.replace('_', ' ').title()}: PASSED")
                elif results["status"] == "FAILED":
                    failed_checks += 1
                    print(f"❌ {category.replace('_', ' ').title()}: FAILED")
                else:
                    pending_checks += 1
                    print(f"📝 {category.replace('_', ' ').title()}: {results['status']}")
        
        # Overall status
        if failed_checks > 0:
            overall_status = "FAILED"
            status_emoji = "❌"
        elif pending_checks > 0:
            overall_status = "READY"
            status_emoji = "📝"
        else:
            overall_status = "PASSED"
            status_emoji = "✅"
        
        print(f"\n{status_emoji} Overall Status: {overall_status}")
        print(f"📈 Summary: {passed_checks} passed, {failed_checks} failed, {pending_checks} pending")
        
        return {
            "overall_status": overall_status,
            "summary": {
                "total_checks": total_checks,
                "passed": passed_checks,
                "failed": failed_checks,
                "pending": pending_checks
            },
            "details": self.validation_results,
            "recommendations": self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on validation results"""
        recommendations = []
        
        # Check if any critical issues found
        if self.validation_results.get("path_manager_consistency", {}).get("status") == "FAILED":
            recommendations.append("Fix path manager inconsistencies before proceeding")
        
        if self.validation_results.get("directory_structure", {}).get("status") == "FAILED":
            recommendations.append("Ensure data directory structure is properly initialized")
        
        # Check raw data availability
        raw_data = self.validation_results.get("directory_structure", {}).get("raw_data", {})
        if not raw_data.get("exists", False):
            recommendations.append("Ensure raw TJA data is available in data/raw/ese/ before running Phase 1")
        elif raw_data.get("file_count", 0) == 0:
            recommendations.append("Raw data directory exists but contains no TJA files")
        
        if not recommendations:
            recommendations.append("Data structure organization is ready for processing")
            recommendations.append("Run Phase 1 to begin data processing pipeline")
        
        return recommendations


def main():
    """Main validation function"""
    print("TJA Generator - Data Flow Validation")
    print("=" * 60)
    
    validator = DataFlowValidator()
    report = validator.validate_all_phases()
    
    # Save detailed report
    report_path = Path("validation_report.json")
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 Detailed report saved to: {report_path}")
    
    # Return appropriate exit code
    if report["overall_status"] == "FAILED":
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
