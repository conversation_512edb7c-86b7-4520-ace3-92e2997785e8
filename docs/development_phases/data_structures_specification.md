# TJA Generator Data Structures Specification

## Document Information
- **Version**: 2.0.0
- **Last Updated**: 2025-07-25
- **Status**: Active
- **Scope**: Complete data organization and schema specifications

## 1. Overview

This document defines the comprehensive data structure organization, schemas, and file formats used across all phases of the TJA Generator system. It establishes the standardized contracts between phases and ensures consistent data flow throughout the processing pipeline.

### 1.1 Core Principles
- **Phase-based Organization**: All outputs strictly organized within `data/phase_X/` directories
- **Standardized Schemas**: Consistent data formats across all phases
- **Forward Compatibility**: Schema versioning for future enhancements
- **Validation Framework**: Comprehensive validation at each phase boundary

## 2. Directory Structure

### 2.1 Root Data Organization
```
data/
├── raw/ese/                    # Raw TJA and audio files (read-only)
│   ├── 01 Pop/                # Genre-based organization
│   ├── 02 Anime/              # 500+ songs per category
│   ├── 03 Vocaloid/           # Extensive collections
│   └── ...                    # Additional genres
├── phase_1/                   # Phase 1 outputs
├── phase_2/                   # Phase 2 outputs
├── phase_3/                   # Phase 3 outputs
├── phase_4/                   # Phase 4 outputs
├── phase_5/                   # Phase 5 outputs
└── phase_6/                   # Phase 6 outputs
```

### 2.2 Phase-Specific Directory Standards
Each phase directory follows this standardized structure:
```
data/phase_X/
├── outputs/                   # Primary phase outputs
├── metadata/                  # Processing metadata
├── validation/                # Validation results
├── logs/                      # Phase-specific logs
└── temp/                      # Temporary processing files
```

## 3. Standardized Output Schema

### 3.1 Base Output Structure
All phase outputs implement the `StandardizedOutput` schema:

```python
@dataclass
class StandardizedOutput:
    phase_number: int              # Phase identifier (1-6)
    phase_name: str               # Human-readable phase name
    success: bool                 # Processing success status
    timestamp: str                # ISO 8601 timestamp
    version: str = "2.0.0"        # Schema version
    
    # Core metrics
    processing_metrics: ProcessingMetrics
    validation_metrics: ValidationMetrics
    hardware_metrics: HardwareMetrics
    
    # Phase-specific outputs
    outputs: Dict[str, Any]       # Phase-specific data
    files: List[str]              # Generated file paths
    directory: str                # Output directory path
```

### 3.2 Metrics Schemas
```python
@dataclass
class ProcessingMetrics:
    files_processed: int
    processing_time_seconds: float
    memory_usage_mb: float
    success_rate: float

@dataclass
class ValidationMetrics:
    validation_passed: bool
    validation_score: float
    validation_errors: List[str]
    validation_warnings: List[str]

@dataclass
class HardwareMetrics:
    cpu_usage_percent: float
    memory_usage_percent: float
    gpu_usage_percent: float
    gpu_memory_usage_mb: float
```

## 4. Phase-Specific Data Contracts

### 4.1 Phase 1: Data Analysis and Preprocessing
**Input**: Raw TJA and audio files from `data/raw/ese/`
**Output Directory**: `data/phase_1/`

**Primary Outputs**:
- `catalog.json`: Complete file catalog with metadata
- `notation_data/`: Extracted TJA notation sequences
- `metadata/`: Separated metadata (titles, artists, etc.)
- `validation_report.json`: Data quality assessment

**Schema**:
```python
@dataclass
class Phase1Output(StandardizedOutput):
    catalog_entries: int
    notation_files_created: int
    metadata_files_created: int
    validation_summary: Dict[str, Any]
```

### 4.2 Phase 2: Audio Feature Extraction
**Input**: Audio files from Phase 1 catalog
**Output Directory**: `data/phase_2/`

**Primary Outputs**:
- `audio_features/`: Feature tensors [T, 201] per audio file
- `feature_metadata.json`: Feature extraction parameters
- `alignment_data.json`: Temporal alignment information

**Schema**:
```python
@dataclass
class Phase2Output(StandardizedOutput):
    feature_tensor_shape: List[int]  # [T, 201]
    sample_rate: int
    duration_seconds: float
    feature_types: List[str]
    temporal_resolution_ms: float
```

### 4.3 Phase 3: TJA Sequence Processing
**Input**: Notation data from Phase 1, audio features from Phase 2
**Output Directory**: `data/phase_3/`

**Primary Outputs**:
- `tja_sequences/`: Processed sequence tensors [T, 8]
- `sequence_metadata.json`: Sequence processing parameters
- `alignment_validation.json`: Audio-sequence alignment verification

**Schema**:
```python
@dataclass
class Phase3Output(StandardizedOutput):
    sequence_tensor_shape: List[int]  # [T, 8]
    note_types_encoded: List[str]
    difficulty_levels: List[int]
    temporal_alignment_accuracy: float
```

### 4.4 Phase 4: Neural Network Architecture
**Input**: Training data from Phases 2 and 3
**Output Directory**: `data/phase_4/`

**Primary Outputs**:
- `model_architecture/`: Model definition files
- `model_config.json`: Architecture configuration
- `training_setup.json`: Training pipeline configuration

**Schema**:
```python
@dataclass
class Phase4Output(StandardizedOutput):
    model_parameters: int
    model_size_mb: float
    architecture_type: str
    training_config: Dict[str, Any]
```

### 4.5 Phase 5: Model Training and Optimization
**Input**: Model architecture from Phase 4, training data from Phases 2-3
**Output Directory**: `data/phase_5/`

**Primary Outputs**:
- `trained_models/`: Final trained model files
- `checkpoints/`: Training checkpoints
- `training_logs/`: Comprehensive training logs
- `optimization_results.json`: Training optimization results

**Schema**:
```python
@dataclass
class Phase5Output(StandardizedOutput):
    final_model_path: str
    training_epochs: int
    final_validation_accuracy: float
    training_time_hours: float
    best_checkpoint_path: str
```

### 4.6 Phase 6: Inference Pipeline and Validation
**Input**: Trained model from Phase 5, validation data
**Output Directory**: `data/phase_6/`

**Primary Outputs**:
- `inference_results/`: Generated TJA charts
- `validation_reports/`: Quality assessment reports
- `performance_benchmarks.json`: System performance metrics

**Schema**:
```python
@dataclass
class Phase6Output(StandardizedOutput):
    charts_generated: int
    average_generation_time_seconds: float
    quality_score: float
    validation_passed: bool
```

## 5. Data Flow and Dependencies

### 5.1 Inter-Phase Data Flow
```
Raw Data (data/raw/ese/)
    ↓ [TJA files + Audio files]
Phase 1 (data/phase_1/)
    ↓ [catalog.json + notation_data/]
Phase 2 (data/phase_2/)
    ↓ [audio_features/ [T, 201]]
Phase 3 (data/phase_3/)
    ↓ [tja_sequences/ [T, 8]]
Phase 4 (data/phase_4/)
    ↓ [model_architecture/]
Phase 5 (data/phase_5/)
    ↓ [trained_models/]
Phase 6 (data/phase_6/)
    ↓ [inference_results/]
```

### 5.2 Data Validation Points
- **Phase 1 → Phase 2**: Catalog integrity and file availability
- **Phase 2 → Phase 3**: Feature tensor dimensions and temporal alignment
- **Phase 3 → Phase 4**: Sequence format and training data completeness
- **Phase 4 → Phase 5**: Model architecture compatibility
- **Phase 5 → Phase 6**: Model loading and inference capability

## 6. File Format Standards

### 6.1 JSON Files
- **Encoding**: UTF-8
- **Format**: Pretty-printed with 2-space indentation
- **Schema Validation**: JSON Schema validation required

### 6.2 Tensor Files
- **Format**: PyTorch tensors (.pt) or NumPy arrays (.npy)
- **Precision**: Float32 for features, appropriate types for sequences
- **Compression**: Optional compression for large files

### 6.3 TJA Files
- **Encoding**: UTF-8
- **Line Endings**: Unix-style (\n)
- **Validation**: Full TJA format compliance required

## 7. Validation Framework

### 7.1 Schema Validation
All outputs must pass schema validation using the `SchemaValidator` class:
```python
class SchemaValidator:
    def validate_phase_output(self, phase_number: int, output_data: Dict) -> ValidationResult
    def validate_data_flow(self, source_phase: int, target_phase: int) -> ValidationResult
    def validate_file_formats(self, file_paths: List[str]) -> ValidationResult
```

### 7.2 Data Integrity Checks
- File existence and accessibility
- Tensor shape and type validation
- JSON schema compliance
- Cross-phase data consistency

## 8. Path Management

### 8.1 Standardized Path Resolution
All phases use the `PathManager` class for consistent path resolution:
```python
path_manager = PathManager()
phase_output_path = path_manager.get_phase_output_path(phase_number)
```

### 8.2 Path Validation
- Automatic directory creation
- Permission validation
- Path normalization
- Cross-platform compatibility

## 9. Version Control and Migration

### 9.1 Schema Versioning
- Current Version: 2.0.0
- Backward compatibility maintained for 1.x versions
- Migration scripts available for version upgrades

### 9.2 Data Migration
- Automated migration tools for schema updates
- Validation of migrated data
- Rollback capabilities for failed migrations

## 10. Performance Considerations

### 10.1 Memory Management
- Lazy loading for large datasets
- Memory-mapped files for tensor data
- Garbage collection optimization

### 10.2 Storage Optimization
- Compression for archival data
- Efficient tensor storage formats
- Cleanup of temporary files

---

**Document Maintenance**: This specification is maintained alongside code changes and updated with each major release. All changes must be validated against existing implementations.
