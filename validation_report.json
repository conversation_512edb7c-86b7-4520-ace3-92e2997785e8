{"overall_status": "PASSED", "summary": {"total_checks": 2, "passed": 2, "failed": 0, "pending": 0}, "details": {"directory_structure": {"status": "PASSED", "phase_directories": {"phase_1": {"exists": true, "path": "data\\phase_1", "is_directory": true}, "phase_2": {"exists": true, "path": "data\\phase_2", "is_directory": true}, "phase_3": {"exists": true, "path": "data\\phase_3", "is_directory": true}, "phase_4": {"exists": true, "path": "data\\phase_4", "is_directory": true}, "phase_5": {"exists": true, "path": "data\\phase_5", "is_directory": true}, "phase_6": {"exists": true, "path": "data\\phase_6", "is_directory": true}}, "raw_data": {"exists": true, "path": "data\\raw\\ese", "file_count": 2800}}, "path_manager_consistency": {"status": "PASSED", "paths": {"phase_1": {"expected": "data\\phase_1", "actual": "D:\\TJAGenerator\\data\\phase_1", "consistent": true}, "phase_2": {"expected": "data\\phase_2", "actual": "D:\\TJAGenerator\\data\\phase_2", "consistent": true}, "phase_3": {"expected": "data\\phase_3", "actual": "D:\\TJAGenerator\\data\\phase_3", "consistent": true}, "phase_4": {"expected": "data\\phase_4", "actual": "D:\\TJAGenerator\\data\\phase_4", "consistent": true}, "phase_5": {"expected": "data\\phase_5", "actual": "D:\\TJAGenerator\\data\\phase_5", "consistent": true}, "phase_6": {"expected": "data\\phase_6", "actual": "D:\\TJAGenerator\\data\\phase_6", "consistent": true}}}, "inter_phase_contracts": {"phase_1_to_2": {"expected_files": ["catalog.json", "notation_data/", "metadata/", "validation_report.json"], "found_files": [], "missing_files": ["catalog.json", "notation_data/", "metadata/", "validation_report.json"], "status": "PENDING"}, "phase_2_to_3": {"status": "PENDING", "note": "Phase 2 not yet executed"}, "phase_3_to_4": {"status": "PENDING", "note": "Phase 3 not yet executed"}, "phase_4_to_5": {"status": "PENDING", "note": "Phase 4 not yet executed"}, "phase_5_to_6": {"status": "PENDING", "note": "Phase 5 not yet executed"}}}, "recommendations": ["Data structure organization is ready for processing", "Run Phase 1 to begin data processing pipeline"]}