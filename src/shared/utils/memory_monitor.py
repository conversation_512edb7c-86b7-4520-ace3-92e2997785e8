"""
Memory Monitor

GPU and system memory monitoring utilities for Phase 3 training.
Provides real-time memory tracking and automatic optimization.
"""

import torch
import psutil
import gc
from typing import Dict, Optional, Tuple
from dataclasses import dataclass
import time

from src.shared.logging.unified_logger import get_unified_logger


@dataclass
class MemoryStats:
    """Memory usage statistics"""
    gpu_allocated_gb: float
    gpu_reserved_gb: float
    gpu_free_gb: float
    gpu_total_gb: float
    gpu_utilization_percent: float
    system_memory_gb: float
    system_memory_percent: float
    system_available_gb: float


class MemoryMonitor:
    """Real-time memory monitoring and optimization"""
    
    def __init__(self, device: Optional[torch.device] = None):
        self.device = device or torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.logger = get_unified_logger(f"{__name__}.MemoryMonitor")
        self.cuda_available = torch.cuda.is_available()
        
        # Memory tracking
        self.peak_memory_gb = 0.0
        self.memory_history = []
        self.oom_count = 0
        
        # Thresholds for automatic optimization
        self.gpu_memory_threshold = 0.85  # 85% GPU memory usage triggers optimization
        self.system_memory_threshold = 0.90  # 90% system memory usage triggers optimization
        
        if self.cuda_available:
            self.gpu_total_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        else:
            self.gpu_total_memory = 0.0
    
    def get_memory_stats(self) -> MemoryStats:
        """Get current memory statistics"""
        # GPU memory
        if self.cuda_available:
            gpu_allocated = torch.cuda.memory_allocated(0) / (1024**3)
            gpu_reserved = torch.cuda.memory_reserved(0) / (1024**3)
            gpu_free = self.gpu_total_memory - gpu_reserved
            gpu_utilization = (gpu_reserved / self.gpu_total_memory) * 100
        else:
            gpu_allocated = gpu_reserved = gpu_free = gpu_utilization = 0.0

        # System memory
        memory = psutil.virtual_memory()
        system_memory_gb = memory.used / (1024**3)
        system_memory_percent = memory.percent
        system_available_gb = memory.available / (1024**3)

        stats = MemoryStats(
            gpu_allocated_gb=gpu_allocated,
            gpu_reserved_gb=gpu_reserved,
            gpu_free_gb=gpu_free,
            gpu_total_gb=self.gpu_total_memory,
            gpu_utilization_percent=gpu_utilization,
            system_memory_gb=system_memory_gb,
            system_memory_percent=system_memory_percent,
            system_available_gb=system_available_gb
        )

    def get_current_usage(self) -> float:
        """Get current memory usage in MB (for compatibility with base classes)"""
        try:
            stats = self.get_memory_stats()
            # Return system memory usage in MB
            return stats.system_memory_gb * 1024
        except Exception:
            # Fallback to basic system memory check
            try:
                import psutil
                memory = psutil.virtual_memory()
                return memory.used / (1024**2)  # Convert to MB
            except Exception:
                return 0.0
        
        # Update peak memory
        if gpu_reserved > self.peak_memory_gb:
            self.peak_memory_gb = gpu_reserved
        
        # Store in history
        self.memory_history.append({
            "timestamp": time.time(),
            "gpu_reserved_gb": gpu_reserved,
            "system_memory_percent": system_memory_percent
        })
        
        # Keep only last 100 entries
        if len(self.memory_history) > 100:
            self.memory_history = self.memory_history[-100:]
        
        return stats
    
    def cleanup_memory(self, aggressive: bool = False):
        """Clean up GPU and system memory"""
        if self.cuda_available:
            # Clear GPU cache
            torch.cuda.empty_cache()
            
            if aggressive:
                # Force garbage collection
                gc.collect()
                torch.cuda.empty_cache()
                
                # Clear all non-essential cached tensors
                torch.cuda.synchronize()
    
    def check_memory_pressure(self) -> Tuple[bool, str]:
        """
        Check if system is under memory pressure
        
        Returns:
            (is_under_pressure, reason)
        """
        stats = self.get_memory_stats()
        
        if stats.gpu_utilization_percent > self.gpu_memory_threshold * 100:
            return True, f"GPU memory usage high: {stats.gpu_utilization_percent:.1f}%"
        
        if stats.system_memory_percent > self.system_memory_threshold * 100:
            return True, f"System memory usage high: {stats.system_memory_percent:.1f}%"
        
        return False, "Memory usage normal"
    
    def suggest_optimizations(self) -> Dict[str, str]:
        """Suggest memory optimizations based on current usage"""
        stats = self.get_memory_stats()
        suggestions = {}
        
        if stats.gpu_utilization_percent > 80:
            suggestions["gpu"] = "Consider reducing batch size or sequence length"
        
        if stats.system_memory_percent > 85:
            suggestions["system"] = "Consider reducing number of data loader workers"
        
        if stats.gpu_free_gb < 1.0:
            suggestions["gpu_critical"] = "GPU memory critically low - immediate optimization needed"
        
        if stats.system_available_gb < 2.0:
            suggestions["system_critical"] = "System memory critically low - reduce memory usage"
        
        return suggestions
    
    def log_memory_status(self, prefix: str = ""):
        """Log current memory status"""
        stats = self.get_memory_stats()
        
        if prefix:
            prefix = f"{prefix} - "
        
        self.logger.info(f"{prefix}Memory Status:")
        if self.cuda_available:
            self.logger.info(f"  GPU: {stats.gpu_reserved_gb:.2f}GB / {stats.gpu_total_gb:.2f}GB ({stats.gpu_utilization_percent:.1f}%)")
        self.logger.info(f"  System: {stats.system_memory_gb:.2f}GB ({stats.system_memory_percent:.1f}%)")
        
        # Check for warnings
        is_pressure, reason = self.check_memory_pressure()
        if is_pressure:
            self.logger.warning(f"  ⚠️  {reason}")
        
        # Log suggestions
        suggestions = self.suggest_optimizations()
        for key, suggestion in suggestions.items():
            self.logger.warning(f"  💡 {suggestion}")
    
    def estimate_batch_memory(self, batch_size: int, sequence_length: int, 
                             feature_dim: int = 201, hidden_dim: int = 128) -> float:
        """
        Estimate memory usage for a given batch configuration
        
        Returns:
            Estimated GPU memory usage in GB
        """
        # Input tensors
        input_memory = batch_size * sequence_length * feature_dim * 4 / (1024**3)  # float32
        
        # Hidden states (rough estimate)
        hidden_memory = batch_size * sequence_length * hidden_dim * 4 / (1024**3)
        
        # Attention matrices (sequence_length^2 for self-attention)
        attention_memory = batch_size * sequence_length * sequence_length * 4 / (1024**3)
        
        # Gradients (roughly same as parameters + activations)
        gradient_memory = (input_memory + hidden_memory + attention_memory) * 2
        
        total_memory = input_memory + hidden_memory + attention_memory + gradient_memory
        
        return total_memory
    
    def find_optimal_batch_size(self, max_sequence_length: int, 
                               feature_dim: int = 201, hidden_dim: int = 128,
                               safety_margin: float = 0.8) -> int:
        """
        Find optimal batch size that fits in available GPU memory
        
        Args:
            max_sequence_length: Maximum sequence length
            feature_dim: Feature dimension
            hidden_dim: Hidden dimension
            safety_margin: Use only this fraction of available memory
            
        Returns:
            Optimal batch size
        """
        if not self.cuda_available:
            return 1
        
        available_memory = self.gpu_total_memory * safety_margin
        
        # Start with batch size 1 and increase until memory limit
        optimal_batch_size = 1
        
        for batch_size in range(1, 17):  # Test up to batch size 16
            estimated_memory = self.estimate_batch_memory(
                batch_size, max_sequence_length, feature_dim, hidden_dim
            )
            
            if estimated_memory <= available_memory:
                optimal_batch_size = batch_size
            else:
                break
        
        return optimal_batch_size
    
    def monitor_training_step(self, step: int, log_frequency: int = 100):
        """Monitor memory during training step"""
        if step % log_frequency == 0:
            self.log_memory_status(f"Step {step}")
            
            # Check for memory pressure
            is_pressure, reason = self.check_memory_pressure()
            if is_pressure:
                self.cleanup_memory(aggressive=True)
                self.logger.warning(f"Performed aggressive memory cleanup: {reason}")
    
    def handle_oom_error(self, error: Exception) -> bool:
        """
        Handle out-of-memory error
        
        Returns:
            True if recovery was attempted, False if unrecoverable
        """
        self.oom_count += 1
        self.logger.error(f"Out of memory error #{self.oom_count}: {error}")
        
        # Attempt recovery
        self.cleanup_memory(aggressive=True)
        
        # Log memory status after cleanup
        self.log_memory_status("After OOM cleanup")
        
        # Suggest optimizations
        suggestions = self.suggest_optimizations()
        for key, suggestion in suggestions.items():
            self.logger.error(f"  🔧 {suggestion}")
        
        return True  # Indicate recovery was attempted
    
    def get_memory_summary(self) -> Dict[str, float]:
        """Get summary of memory usage statistics"""
        stats = self.get_memory_stats()
        
        return {
            "current_gpu_usage_gb": stats.gpu_reserved_gb,
            "peak_gpu_usage_gb": self.peak_memory_gb,
            "gpu_utilization_percent": stats.gpu_utilization_percent,
            "system_memory_percent": stats.system_memory_percent,
            "oom_count": self.oom_count,
            "gpu_total_gb": stats.gpu_total_gb
        }


def setup_memory_optimized_environment():
    """Setup environment for memory-optimized training"""
    # Set memory growth for PyTorch
    if torch.cuda.is_available():
        # Enable memory efficient attention if available
        try:
            torch.backends.cuda.enable_flash_sdp(True)
        except:
            pass

        # Set memory fraction (more conservative)
        torch.cuda.set_per_process_memory_fraction(0.6)  # Use 60% of GPU memory

    # Set environment variables for memory optimization
    import os
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:64"  # Smaller splits

    # Force garbage collection
    import gc
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

    return MemoryMonitor()


# Context manager for memory monitoring
class MemoryContext:
    """Context manager for monitoring memory usage in code blocks"""
    
    def __init__(self, monitor: MemoryMonitor, name: str = "operation"):
        self.monitor = monitor
        self.name = name
        self.start_stats = None
    
    def __enter__(self):
        self.start_stats = self.monitor.get_memory_stats()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_stats = self.monitor.get_memory_stats()
        
        gpu_diff = end_stats.gpu_reserved_gb - self.start_stats.gpu_reserved_gb
        system_diff = end_stats.system_memory_percent - self.start_stats.system_memory_percent
        
        self.monitor.logger.info(
            f"Memory usage for {self.name}: "
            f"GPU +{gpu_diff:.3f}GB, System +{system_diff:.1f}%"
        )
        
        if exc_type is not None and "out of memory" in str(exc_val).lower():
            self.monitor.handle_oom_error(exc_val)
