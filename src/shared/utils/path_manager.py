"""
Simplified Path Management System

Provides basic path handling for the TJA Generator system.
"""

import os
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
from enum import Enum

from src.shared.logging.unified_logger import get_unified_logger


class PathType(Enum):
    """Enumeration of path types for consistent handling"""
    DATA_RAW = "data_raw"
    DATA_PROCESSED = "data_processed"
    AUDIO_FEATURES = "audio_features"
    MODELS = "models"
    OUTPUTS = "outputs"
    LOGS = "logs"
    CACHE = "cache"
    TEMP = "temp"
    CONFIG = "config"


class PathManager:
    """
    Simplified path management system
    
    Provides basic path resolution and standardized paths
    for the TJA Generator system.
    """
    
    def __init__(self, workspace_root: Optional[Union[str, Path]] = None):
        self.logger = get_unified_logger(f"{__name__}.PathManager")
        
        # Initialize workspace root
        if workspace_root:
            self.workspace_root = Path(workspace_root).resolve()
        else:
            self.workspace_root = Path.cwd()
        
        # Define standard paths
        self.standard_paths = {
            PathType.DATA_RAW: self.workspace_root / "data" / "raw",
            PathType.DATA_PROCESSED: self.workspace_root / "data" / "processed",
            PathType.AUDIO_FEATURES: self.workspace_root / "data" / "processed" / "audio_features",
            PathType.MODELS: self.workspace_root / "models",
            PathType.OUTPUTS: self.workspace_root / "outputs",
            PathType.LOGS: self.workspace_root / "logs",
            PathType.CACHE: self.workspace_root / "cache",
            PathType.TEMP: self.workspace_root / "temp",
            PathType.CONFIG: self.workspace_root / "config"
        }
        
        # Create a paths attribute for backward compatibility
        self.paths = self

        self.logger.info(f"PathManager initialized with workspace: {self.workspace_root}")

    def get_phase_output_path(self, phase_number: int) -> Path:
        """Get output path for specific phase"""
        return self.workspace_root / "data" / f"phase_{phase_number}"
    
    def resolve_path(self, path: Union[str, Path], 
                    relative_to: Optional[Union[str, Path, PathType]] = None) -> Path:
        """
        Resolve path with consistent normalization
        
        Args:
            path: Path to resolve
            relative_to: Base path or PathType to resolve relative to
            
        Returns:
            Resolved and normalized Path object
        """
        # Convert to Path object
        path_obj = Path(path)
        
        # Determine base path
        if relative_to is None:
            base_path = self.workspace_root
        elif isinstance(relative_to, PathType):
            base_path = self.get_standardized_path(relative_to)
        else:
            base_path = Path(relative_to)
        
        # Resolve path
        if path_obj.is_absolute():
            resolved = path_obj
        else:
            resolved = base_path / path_obj
        
        # Normalize path
        resolved = resolved.resolve()
        
        return resolved
    
    def get_standardized_path(self, path_type: PathType, 
                            subpath: Optional[str] = None) -> Path:
        """
        Get standardized path for specific type
        
        Args:
            path_type: Type of path to get
            subpath: Optional subpath to append
            
        Returns:
            Standardized path
        """
        base_path = self.standard_paths.get(path_type, self.workspace_root)
        
        if subpath:
            return base_path / subpath
        
        return base_path
    
    def ensure_directory_exists(self, path_type: PathType, 
                               subpath: Optional[str] = None) -> Path:
        """
        Ensure directory exists and return path
        
        Args:
            path_type: Type of path
            subpath: Optional subpath
            
        Returns:
            Path to created directory
        """
        dir_path = self.get_standardized_path(path_type, subpath)
        dir_path.mkdir(parents=True, exist_ok=True)
        return dir_path
    
    def validate_path_exists(self, path: Union[str, Path, PathType], 
                           subpath: Optional[str] = None) -> bool:
        """
        Validate that a path exists
        
        Args:
            path: Path to validate
            subpath: Optional subpath
            
        Returns:
            True if path exists
        """
        if isinstance(path, PathType):
            check_path = self.get_standardized_path(path, subpath)
        else:
            check_path = self.resolve_path(path)
            if subpath:
                check_path = check_path / subpath
        
        return check_path.exists()
    
    def get_file_pattern_paths(self, pattern: str, 
                              base_path: Optional[Union[str, Path, PathType]] = None,
                              recursive: bool = False) -> List[Path]:
        """
        Get list of files matching pattern
        
        Args:
            pattern: File pattern (e.g., "*.tja", "**/*.mp3")
            base_path: Base directory to search in
            recursive: Whether to search recursively
            
        Returns:
            List of matching file paths
        """
        if base_path is None:
            search_path = self.workspace_root
        elif isinstance(base_path, PathType):
            search_path = self.get_standardized_path(base_path)
        else:
            search_path = self.resolve_path(base_path)
        
        if not search_path.exists():
            self.logger.warning(f"Search path does not exist: {search_path}")
            return []
        
        try:
            if recursive or '**' in pattern:
                files = list(search_path.rglob(pattern))
            else:
                files = list(search_path.glob(pattern))
            
            # Filter to only files and normalize paths
            result = []
            for file_path in files:
                if file_path.is_file():
                    normalized = self.resolve_path(file_path)
                    result.append(normalized)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error searching for pattern {pattern} in {search_path}: {e}")
            return []
    
    def normalize_path_separators(self, path: Union[str, Path], 
                                 force_forward: bool = True) -> str:
        """
        Normalize path separators
        
        Args:
            path: Path to normalize
            force_forward: Whether to force forward slashes
            
        Returns:
            Normalized path string
        """
        path_str = str(path)
        
        if force_forward:
            return path_str.replace('\\', '/')
        else:
            return os.path.normpath(path_str)
    
    def get_relative_to_workspace(self, path: Union[str, Path]) -> str:
        """
        Get path relative to workspace root with forward slashes
        
        Args:
            path: Path to make relative
            
        Returns:
            Relative path string with forward slashes
        """
        abs_path = self.resolve_path(path)
        try:
            rel_path = abs_path.relative_to(self.workspace_root)
            return self.normalize_path_separators(rel_path, force_forward=True)
        except ValueError:
            # Path is not under workspace, return absolute path
            return self.normalize_path_separators(abs_path, force_forward=True)
    
    def get_path_info(self) -> Dict[str, Any]:
        """Get information about all standardized paths"""
        return {
            "workspace_root": self.normalize_path_separators(self.workspace_root),
            "paths": {
                path_type.value: self.normalize_path_separators(path)
                for path_type, path in self.standard_paths.items()
            }
        }
