"""
Unified Configuration Manager for TJA Generator

Centralizes all configuration management with consistent path handling,
environment detection, and hardware optimization settings.
"""

import json
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict

from src.shared.utils.base_classes import BaseConfigManager
from src.shared.logging.unified_logger import get_unified_logger
from src.shared.utils.hardware_monitor import get_system_info
from src.shared.utils.path_manager import PathManager, PathType


@dataclass
class HardwareConfig:
    """Hardware-specific configuration"""
    target_gpu: str = "RTX 3070"
    max_vram_gb: float = 8.0
    max_system_memory_gb: float = 32.0
    cpu_cores: int = 16
    parallel_workers: int = 12
    batch_size: int = 4
    memory_utilization_target: float = 0.75  # 75% utilization target


@dataclass
class PathConfig:
    """Standardized path configuration"""
    workspace_root: str = "."
    data_directory: str = "data"
    models_directory: str = "models"
    outputs_directory: str = "outputs"
    logs_directory: str = "logs"
    cache_directory: str = "cache"
    temp_directory: str = "temp"
    
    # Phase-specific directories
    raw_data_directory: str = "data/raw"
    processed_data_directory: str = "data/processed"
    audio_features_directory: str = "data/processed/audio_features"
    training_data_directory: str = "data/processed/training"
    
    # Output directories
    phase1_outputs: str = "outputs/phase1"
    phase2_outputs: str = "outputs/phase2"
    phase3_outputs: str = "outputs/phase3"
    phase4_outputs: str = "outputs/phase4"
    phase5_outputs: str = "outputs/phase5"
    phase6_outputs: str = "outputs/phase6"


@dataclass
class ProcessingConfig:
    """Processing pipeline configuration"""
    encoding_detection_enabled: bool = True
    supported_encodings: list = None
    audio_formats: list = None
    tja_validation_enabled: bool = True
    quality_assessment_enabled: bool = True
    memory_monitoring_enabled: bool = True
    
    def __post_init__(self):
        if self.supported_encodings is None:
            self.supported_encodings = ["utf-8", "utf-8-sig", "shift-jis"]
        if self.audio_formats is None:
            self.audio_formats = [".mp3", ".wav", ".ogg", ".flac", ".m4a"]


@dataclass
class LoggingConfig:
    """Logging configuration"""
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    enable_file_logging: bool = True
    max_log_file_size_mb: int = 10
    log_rotation_count: int = 5
    console_logging_enabled: bool = True


class UnifiedConfigManager(BaseConfigManager):
    """
    Unified configuration manager for the entire TJA Generator system
    
    Provides centralized configuration management with:
    - Consistent path handling across all phases
    - Hardware-specific optimization settings
    - Environment-aware configuration loading
    - Standardized configuration structure
    """
    
    def __init__(self, workspace_root: Union[str, Path] = "."):
        super().__init__(workspace_root)

        # Initialize path manager for standardized paths
        self.path_manager = PathManager(self.workspace_root)

        # Initialize configuration components
        self.hardware_config = self._detect_hardware_config()
        self.path_config = self._create_standardized_path_config()
        self.processing_config = ProcessingConfig()
        self.logging_config = LoggingConfig()

        # Create essential directories
        self._ensure_essential_directories()

        self.logger.info("Unified configuration manager initialized with standardized paths")
    
    def _detect_hardware_config(self) -> HardwareConfig:
        """Detect and configure hardware-specific settings"""
        try:
            system_info = get_system_info()
            
            # Extract hardware information
            total_memory_gb = system_info.get("memory", {}).get("total_gb", 32.0)
            cpu_cores = system_info.get("cpu", {}).get("logical_cores", 16)
            gpu_info = system_info.get("gpu", {})
            
            # Determine optimal settings based on detected hardware
            if "RTX 3070" in str(gpu_info.get("name", "")):
                return HardwareConfig(
                    target_gpu="RTX 3070",
                    max_vram_gb=8.0,
                    max_system_memory_gb=min(total_memory_gb * 0.8, 32.0),
                    cpu_cores=cpu_cores,
                    parallel_workers=min(cpu_cores - 4, 12),
                    batch_size=4,
                    memory_utilization_target=0.75
                )
            else:
                # Generic configuration for other hardware
                return HardwareConfig(
                    max_system_memory_gb=min(total_memory_gb * 0.7, 16.0),
                    cpu_cores=cpu_cores,
                    parallel_workers=min(cpu_cores // 2, 8),
                    batch_size=2,
                    memory_utilization_target=0.6
                )
                
        except Exception as e:
            self.logger.warning(f"Hardware detection failed: {e}, using defaults")
            return HardwareConfig()

    def _create_standardized_path_config(self) -> PathConfig:
        """Create path configuration using standardized path manager"""
        return PathConfig(
            workspace_root=str(self.workspace_root),
            data_directory=str(self.path_manager.get_standardized_path(PathType.DATA_RAW).parent),
            models_directory=str(self.path_manager.get_standardized_path(PathType.MODELS)),
            outputs_directory=str(self.path_manager.get_standardized_path(PathType.OUTPUTS)),
            logs_directory=str(self.path_manager.get_standardized_path(PathType.LOGS)),
            cache_directory=str(self.path_manager.get_standardized_path(PathType.CACHE)),
            temp_directory=str(self.path_manager.get_standardized_path(PathType.TEMP)),

            # Data subdirectories
            raw_data_directory=str(self.path_manager.get_standardized_path(PathType.DATA_RAW)),
            processed_data_directory=str(self.path_manager.get_standardized_path(PathType.DATA_PROCESSED)),
            audio_features_directory=str(self.path_manager.get_standardized_path(PathType.AUDIO_FEATURES)),
            training_data_directory=str(self.path_manager.get_standardized_path(PathType.DATA_PROCESSED, "training")),

            # Phase outputs
            phase1_outputs=str(self.path_manager.paths.get_phase_output_path(1)),
            phase2_outputs=str(self.path_manager.paths.get_phase_output_path(2)),
            phase3_outputs=str(self.path_manager.paths.get_phase_output_path(3)),
            phase4_outputs=str(self.path_manager.paths.get_phase_output_path(4)),
            phase5_outputs=str(self.path_manager.paths.get_phase_output_path(5)),
            phase6_outputs=str(self.path_manager.paths.get_phase_output_path(6))
        )

    def _ensure_essential_directories(self):
        """Create essential directories using path manager"""
        essential_path_types = [
            PathType.DATA_RAW,
            PathType.DATA_PROCESSED,
            PathType.AUDIO_FEATURES,
            PathType.MODELS,
            PathType.OUTPUTS,
            PathType.LOGS,
            PathType.CACHE,
            PathType.TEMP
        ]

        for path_type in essential_path_types:
            self.path_manager.ensure_directory_exists(path_type)

        # Ensure phase output directories
        for phase_num in range(1, 7):
            phase_path = self.path_manager.paths.get_phase_output_path(phase_num)
            phase_path.mkdir(parents=True, exist_ok=True)
    
    def get_phase_config(self, phase_number: int) -> Dict[str, Any]:
        """Get configuration for a specific phase"""
        base_config = {
            "hardware": asdict(self.hardware_config),
            "paths": asdict(self.path_config),
            "processing": asdict(self.processing_config),
            "logging": asdict(self.logging_config)
        }
        
        # Add phase-specific configurations
        phase_specific = self._get_phase_specific_config(phase_number)
        base_config.update(phase_specific)
        
        return base_config
    
    def _get_phase_specific_config(self, phase_number: int) -> Dict[str, Any]:
        """Get phase-specific configuration settings"""
        phase_configs = {
            1: {
                "phase_name": "Data Analysis and Preprocessing",
                "output_directory": self.path_config.phase1_outputs,
                "catalog_file": "data/processed/catalog.json",
                "enable_parallel_processing": True,
                "validation_strictness": "high"
            },
            2: {
                "phase_name": "Audio Feature Extraction",
                "output_directory": self.path_config.phase2_outputs,
                "feature_tensor_directory": self.path_config.audio_features_directory,
                "enable_gpu_acceleration": True,
                "feature_dimensions": 201
            },
            3: {
                "phase_name": "Model Training and Generation",
                "output_directory": self.path_config.phase3_outputs,
                "model_architecture": "transformer",
                "enable_mixed_precision": True,
                "checkpoint_frequency": 100
            },
            4: {
                "phase_name": "Integration and Deployment",
                "output_directory": self.path_config.phase4_outputs,
                "enable_api_server": True,
                "enable_cli_interface": True,
                "quality_assessment_enabled": True
            },
            5: {
                "phase_name": "Advanced Training Optimization",
                "output_directory": self.path_config.phase5_outputs,
                "enable_hyperparameter_optimization": True,
                "enable_curriculum_learning": True,
                "enable_model_ensemble": True
            },
            6: {
                "phase_name": "Inference Pipeline and Validation",
                "output_directory": self.path_config.phase6_outputs,
                "enable_performance_benchmarking": True,
                "enable_production_validation": True,
                "inference_batch_size": 32
            }
        }
        
        return phase_configs.get(phase_number, {})
    
    def save_config_to_file(self, output_path: Union[str, Path]):
        """Save current configuration to file"""
        config_file = self.resolve_path(output_path)
        
        full_config = {
            "hardware": asdict(self.hardware_config),
            "paths": asdict(self.path_config),
            "processing": asdict(self.processing_config),
            "logging": asdict(self.logging_config),
            "metadata": {
                "generated_by": "UnifiedConfigManager",
                "workspace_root": str(self.workspace_root),
                "config_version": "1.0.0"
            }
        }
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(full_config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Configuration saved to {config_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")
    
    def load_config_from_file(self, config_path: Union[str, Path]) -> bool:
        """Load configuration from file"""
        config_data = self.load_config_file(config_path)
        
        if not config_data:
            return False
        
        try:
            # Update configurations from loaded data
            if "hardware" in config_data:
                self.hardware_config = HardwareConfig(**config_data["hardware"])
            
            if "paths" in config_data:
                self.path_config = PathConfig(**config_data["paths"])
            
            if "processing" in config_data:
                self.processing_config = ProcessingConfig(**config_data["processing"])
            
            if "logging" in config_data:
                self.logging_config = LoggingConfig(**config_data["logging"])
            
            self.logger.info(f"Configuration loaded from {config_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load configuration: {e}")
            return False
    
    def get_memory_optimized_config(self) -> Dict[str, Any]:
        """Get memory-optimized configuration based on available resources"""
        # This will be implemented to work with the resource manager
        # to provide dynamic configuration based on current system state
        base_config = self.get_phase_config(1)  # Use phase 1 as base
        
        # Apply memory optimizations
        memory_ratio = 0.75  # Target 75% memory utilization
        
        optimized_config = base_config.copy()
        optimized_config["hardware"]["memory_utilization_target"] = memory_ratio
        optimized_config["hardware"]["batch_size"] = max(1, int(
            self.hardware_config.batch_size * memory_ratio
        ))
        
        return optimized_config
