"""
Unified Logging System for TJA Generator

This module provides centralized logging configuration and management
for the entire TJA Generator system, ensuring consistent formatting
and eliminating duplicate log messages.
"""

from .unified_logger import (
    get_unified_logger,
    set_global_log_level,
    get_logging_stats,
    log_system_info,
    log_phase_start,
    log_phase_complete,
    log_processing_stats,
    log_error_with_context,
    UnifiedLoggerManager
)

__all__ = [
    'get_unified_logger',
    'set_global_log_level', 
    'get_logging_stats',
    'log_system_info',
    'log_phase_start',
    'log_phase_complete',
    'log_processing_stats',
    'log_error_with_context',
    'UnifiedLoggerManager'
]
