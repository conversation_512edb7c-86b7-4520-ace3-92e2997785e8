#!/usr/bin/env python3
"""
Unified Logging System for TJA Generator
Hardware-optimized for RTX 3070 with 32GB RAM

Provides centralized logging configuration, management, and formatting
for all components in the TJA Generator system. Eliminates duplicate
logging and ensures consistent output across all modules.
"""

import logging
import logging.handlers
import sys
import os
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime
import threading


class UnifiedLoggerManager:
    """
    Centralized logging manager for the entire TJA Generator system
    
    Provides singleton-based logging configuration that ensures:
    - Single point of logging configuration
    - Consistent formatting across all modules
    - Proper log rotation and file management
    - No duplicate handlers or messages
    - Thread-safe logger creation
    """
    
    _instance = None
    _lock = threading.Lock()
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._setup_logging_system()
            UnifiedLoggerManager._initialized = True
    
    def _setup_logging_system(self):
        """Initialize the unified logging system"""
        # Clear any existing handlers to prevent duplicates
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # Set root logger level
        root_logger.setLevel(logging.INFO)
        
        # Create unified formatter
        self.formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Setup file logging with rotation
        self._setup_file_logging()
        
        # Setup console logging
        self._setup_console_logging()
        
        # Store configuration
        self.log_file_path = Path("tja_generator.log")
        self.log_level = logging.INFO
        
        # Log the initialization
        logger = logging.getLogger(__name__)
        logger.info("Unified logging system initialized")
    
    def _setup_file_logging(self):
        """Setup file logging with rotation"""
        try:
            # Create rotating file handler (10MB max, 5 backups)
            file_handler = logging.handlers.RotatingFileHandler(
                filename='tja_generator.log',
                maxBytes=10 * 1024 * 1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            file_handler.setLevel(logging.INFO)
            file_handler.setFormatter(self.formatter)
            
            # Add to root logger
            logging.getLogger().addHandler(file_handler)
            
        except Exception as e:
            print(f"Warning: Could not setup file logging: {e}")
    
    def _setup_console_logging(self):
        """Setup console logging"""
        try:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.INFO)
            console_handler.setFormatter(self.formatter)
            
            # Add to root logger
            logging.getLogger().addHandler(console_handler)
            
        except Exception as e:
            print(f"Warning: Could not setup console logging: {e}")
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        Get a logger with the specified name
        
        Args:
            name: Logger name (typically __name__ or module-specific name)
            
        Returns:
            Configured logger instance
        """
        logger = logging.getLogger(name)
        # Don't add handlers - they inherit from root logger
        logger.setLevel(logging.INFO)
        return logger
    
    def set_log_level(self, level: str):
        """Set logging level for all loggers"""
        numeric_level = getattr(logging, level.upper(), logging.INFO)
        
        # Update root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(numeric_level)
        
        # Update all handlers
        for handler in root_logger.handlers:
            handler.setLevel(numeric_level)
        
        self.log_level = numeric_level
    
    def add_custom_handler(self, handler: logging.Handler):
        """Add a custom handler to the logging system"""
        handler.setFormatter(self.formatter)
        logging.getLogger().addHandler(handler)
    
    def get_log_stats(self) -> Dict[str, Any]:
        """Get logging system statistics"""
        root_logger = logging.getLogger()
        return {
            "log_file": str(self.log_file_path),
            "log_level": logging.getLevelName(self.log_level),
            "handlers_count": len(root_logger.handlers),
            "handlers": [type(h).__name__ for h in root_logger.handlers],
            "file_exists": self.log_file_path.exists(),
            "file_size_mb": round(self.log_file_path.stat().st_size / (1024*1024), 2) if self.log_file_path.exists() else 0
        }


# Global instance for easy access
_logger_manager = None


def get_unified_logger(name: str) -> logging.Logger:
    """
    Get a unified logger instance
    
    This is the main function that all modules should use to get their logger.
    It ensures consistent configuration and prevents duplicate handlers.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        Configured logger instance
    """
    global _logger_manager
    if _logger_manager is None:
        _logger_manager = UnifiedLoggerManager()
    
    return _logger_manager.get_logger(name)


def set_global_log_level(level: str):
    """Set the global logging level"""
    global _logger_manager
    if _logger_manager is None:
        _logger_manager = UnifiedLoggerManager()
    
    _logger_manager.set_log_level(level)


def get_logging_stats() -> Dict[str, Any]:
    """Get logging system statistics"""
    global _logger_manager
    if _logger_manager is None:
        _logger_manager = UnifiedLoggerManager()
    
    return _logger_manager.get_log_stats()


def log_system_info():
    """Log system initialization information"""
    logger = get_unified_logger(__name__)
    stats = get_logging_stats()
    
    logger.info("=== TJA Generator Logging System ===")
    logger.info(f"Log file: {stats['log_file']}")
    logger.info(f"Log level: {stats['log_level']}")
    logger.info(f"Handlers: {', '.join(stats['handlers'])}")
    logger.info(f"File size: {stats['file_size_mb']}MB")
    logger.info("=====================================")


# Convenience functions for common logging patterns
def log_phase_start(phase_number: int, logger: logging.Logger):
    """Log phase start with consistent formatting"""
    logger.info("=" * 60)
    logger.info(f"STARTING PHASE {phase_number}")
    logger.info("=" * 60)


def log_phase_complete(phase_number: int, logger: logging.Logger, duration: float):
    """Log phase completion with consistent formatting"""
    logger.info("=" * 60)
    logger.info(f"PHASE {phase_number} COMPLETED SUCCESSFULLY")
    logger.info(f"Duration: {duration:.2f} seconds")
    logger.info("=" * 60)


def log_processing_stats(stats: Dict[str, Any], logger: logging.Logger):
    """Log processing statistics with consistent formatting"""
    logger.info("PROCESSING STATISTICS:")
    for key, value in stats.items():
        if isinstance(value, float):
            logger.info(f"  {key}: {value:.2f}")
        else:
            logger.info(f"  {key}: {value}")


def log_error_with_context(error: Exception, context: str, logger: logging.Logger):
    """Log error with context information"""
    logger.error(f"Error in {context}: {type(error).__name__}: {error}")
    if hasattr(error, '__traceback__') and error.__traceback__:
        import traceback
        logger.debug(f"Traceback: {traceback.format_exc()}")
